import os
import tkinter as tk
from tkinter import filedialog, ttk, messagebox
import base64
import requests
import threading
from tkinter import font
import json
import hashlib
from cryptography.fernet import Fernet

class PrintifyUploader:
    def __init__(self, root):
        self.root = root
        self.root.title("Printify Image Uploader")

        # Set application icon
        try:
            self.root.iconbitmap('app_icon.ico')
        except Exception:
            # If icon file not found, continue without icon
            pass

        self.root.state('zoomed')  # Set to full screen

        self.select_all_state = tk.BooleanVar(value=True)  # Initial state for Select All (True means check all)
        self.config_file = ".printify_config.dat"
        self.api_token = ""
        self.is_uploading = False
        self.upload_stop_requested = False

        self.create_menu()
        self.create_widgets()
        self.load_saved_token()

    def create_menu(self):
        menubar = tk.Menu(self.root)
        self.root.config(menu=menubar)

        # Settings menu
        settings_menu = tk.Menu(menubar, tearoff=0)
        menubar.add_cascade(label="Settings", menu=settings_menu)
        settings_menu.add_command(label="Manage API Token", command=self.open_token_manager)

    def create_widgets(self):
        # Controls frame
        controls_frame = ttk.Frame(self.root)
        controls_frame.pack(side=tk.TOP, fill=tk.X, padx=10, pady=10)

        # Left side - Folder selection
        folder_frame = ttk.Frame(controls_frame)
        folder_frame.pack(side=tk.LEFT, fill=tk.X, expand=True)

        ttk.Label(folder_frame, text="Folder:").pack(side=tk.LEFT, padx=(0, 5))

        self.folder_path_var = tk.StringVar()
        self.folder_entry = ttk.Entry(folder_frame, textvariable=self.folder_path_var, state="readonly", width=50)
        self.folder_entry.pack(side=tk.LEFT, fill=tk.X, expand=True, padx=(0, 5))

        self.browse_button = ttk.Button(folder_frame, text="Browse...", command=self.browse_folder, width=10)
        self.browse_button.pack(side=tk.LEFT, padx=(0, 10))

        # Right side - Upload options
        options_frame = ttk.Frame(controls_frame)
        options_frame.pack(side=tk.RIGHT)

        self.upload_in_order_var = tk.BooleanVar(value=True)
        self.upload_in_order_checkbox = ttk.Checkbutton(options_frame, text="Upload in Order", variable=self.upload_in_order_var)
        self.upload_in_order_checkbox.pack(side=tk.RIGHT)

        self.tree_frame = ttk.Frame(self.root)
        self.tree_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)

        # Add scrollbar to treeview
        self.tree_scrollbar = ttk.Scrollbar(self.tree_frame)
        self.tree_scrollbar.pack(side=tk.RIGHT, fill=tk.Y)

        self.tree = ttk.Treeview(self.tree_frame, columns=("Select", "Image", "Status"), show="headings", selectmode="extended", yscrollcommand=self.tree_scrollbar.set)
        self.tree.heading("Select", text="Select All", command=self.select_all_images)
        self.tree.heading("Image", text="Image Files")
        self.tree.heading("Status", text="Status")
        self.tree.column("Select", width=80, anchor=tk.CENTER)
        self.tree.column("Image", width=400)
        self.tree.column("Status", width=150)
        self.tree.bind('<ButtonRelease-1>', self.toggle_select)

        # Configure status colors
        self.tree.tag_configure("uploading", foreground="blue")
        self.tree.tag_configure("success", foreground="green")
        self.tree.tag_configure("error", foreground="red")
        self.tree.tag_configure("stopped", foreground="orange")

        self.tree.pack(fill=tk.BOTH, expand=True)
        self.tree_scrollbar.config(command=self.tree.yview)

        self.upload_button = ttk.Button(self.root, text="Upload Selected Images", command=self.upload_images)
        self.upload_button.pack(fill=tk.X, pady=10, padx=10)

        # Make the upload button larger and bold
        self.upload_button.config(style='Bold.TButton')
        style = ttk.Style()
        style.configure('Bold.TButton', font=('Helvetica', 10, 'bold'), padding=10)

        # Status bar at the bottom
        self.status_bar = ttk.Frame(self.root, relief=tk.SUNKEN, borderwidth=2)
        self.status_bar.pack(side=tk.BOTTOM, fill=tk.X, pady=(8, 2), padx=2)

        self.token_status_label = ttk.Label(self.status_bar, text="", foreground="gray", padding=(15, 10))
        self.token_status_label.pack(side=tk.LEFT)

    def browse_folder(self):
        folder = filedialog.askdirectory()
        if folder:
            self.folder_path_var.set(folder)
            self.load_images(folder)

    def load_images(self, folder):
        self.tree.delete(*self.tree.get_children())
        for file in os.listdir(folder):
            if file.lower().endswith(('.png', '.jpg', '.jpeg', '.svg')):
                self.tree.insert("", tk.END, values=("", os.path.join(folder, file), ""))
        self.resize_columns()

    def resize_columns(self):
        self.tree.column("Select", width=80)
        self.tree.column("Status", width=150)
        max_width = max(font.Font().measure(self.tree.item(item, "values")[1]) for item in self.tree.get_children())
        self.tree.column("Image", width=max_width + 20)

    def upload_images(self):
        if self.is_uploading:
            # Stop upload if currently uploading
            self.stop_upload()
            return

        if not self.api_token:
            messagebox.showwarning("Missing API Token", "Please set your API token in Settings > Manage API Token.")
            return

        # Clear previous statuses and start upload
        self.clear_upload_statuses()
        self.start_upload()

        if self.upload_in_order_var.get():
            threading.Thread(target=self.upload_images_in_order, daemon=True).start()
        else:
            threading.Thread(target=self.upload_images_parallel, daemon=True).start()

    def clear_upload_statuses(self):
        """Clear all previous upload statuses"""
        for item in self.tree.get_children():
            self.tree.set(item, "Status", "")

    def start_upload(self):
        """Set upload state and update UI"""
        self.is_uploading = True
        self.upload_stop_requested = False
        self.upload_button.config(text="Stop Upload", style='Stop.TButton')

        # Create stop button style
        style = ttk.Style()
        style.configure('Stop.TButton', font=('Helvetica', 10, 'bold'), padding=10)

    def stop_upload(self):
        """Stop the upload process"""
        self.upload_stop_requested = True
        self.is_uploading = False
        self.upload_button.config(text="Upload Selected Images", style='Bold.TButton')

        # Update any uploading statuses to stopped
        for item in self.tree.get_children():
            if self.tree.item(item, "values")[2] == "⏳ Uploading...":
                self.tree.set(item, "Status", "⏹ Stopped")

    def upload_images_parallel(self):
        """Upload images in parallel"""
        for item in self.tree.get_children():
            if self.upload_stop_requested:
                break
            if self.tree.item(item, "values")[0] == "☑":
                image_path = self.tree.item(item, "values")[1]
                self.tree.set(item, "Status", "⏳ Uploading...")
                threading.Thread(target=self.upload_image, args=(item, image_path), daemon=True).start()

        # Reset button when all uploads are started
        if not self.upload_stop_requested:
            self.root.after(1000, self.check_upload_completion)

    def upload_images_in_order(self):
        """Upload images one by one in order"""
        for item in self.tree.get_children():
            if self.upload_stop_requested:
                break
            if self.tree.item(item, "values")[0] == "☑":
                image_path = self.tree.item(item, "values")[1]
                self.tree.set(item, "Status", "⏳ Uploading...")
                self.upload_image(item, image_path)

        # Reset upload state when done
        self.root.after(100, self.finish_upload)

    def check_upload_completion(self):
        """Check if all uploads are completed (for parallel uploads)"""
        uploading_count = 0
        for item in self.tree.get_children():
            status = self.tree.item(item, "values")[2]
            if status == "⏳ Uploading...":
                uploading_count += 1

        if uploading_count == 0:
            self.finish_upload()
        else:
            # Check again in 1 second
            self.root.after(1000, self.check_upload_completion)

    def finish_upload(self):
        """Reset UI after upload completion"""
        if not self.upload_stop_requested:
            self.is_uploading = False
            self.upload_button.config(text="Upload Selected Images", style='Bold.TButton')

    def upload_image(self, item, image_path):
        # Check if stop was requested before starting
        if self.upload_stop_requested:
            self.update_status(item, "⏹ Stopped")
            return

        url = "https://api.printify.com/v1/uploads/images.json"
        headers = {
            "Authorization": f"Bearer {self.api_token}",
            "Content-Type": "application/json"
        }

        try:
            # Check if stop was requested before reading file
            if self.upload_stop_requested:
                self.update_status(item, "⏹ Stopped")
                return

            with open(image_path, "rb") as image_file:
                encoded_string = base64.b64encode(image_file.read()).decode('utf-8')

            # Check if stop was requested before API call
            if self.upload_stop_requested:
                self.update_status(item, "⏹ Stopped")
                return

            data = {
                "file_name": os.path.basename(image_path),
                "contents": encoded_string
            }

            response = requests.post(url, headers=headers, json=data)

            # Check if stop was requested after API call
            if self.upload_stop_requested:
                self.update_status(item, "⏹ Stopped")
                return

            if response.status_code == 200:
                self.update_status(item, "✅ Uploaded")
            else:
                self.update_status(item, f"❌ Failed ({response.status_code})")

        except Exception as e:
            if self.upload_stop_requested:
                self.update_status(item, "⏹ Stopped")
            else:
                self.update_status(item, f"❌ Error: {str(e)[:20]}...")

    def update_status(self, item, status):
        """Update status with thread-safe GUI update and color coding"""
        def update_with_color():
            self.tree.set(item, "Status", status)

            # Apply color tags based on status
            if "Uploading" in status:
                self.tree.set(item, tags=("uploading",))
            elif "Uploaded" in status or "✅" in status:
                self.tree.set(item, tags=("success",))
            elif "Failed" in status or "Error" in status or "❌" in status:
                self.tree.set(item, tags=("error",))
            elif "Stopped" in status or "⏹" in status:
                self.tree.set(item, tags=("stopped",))
            else:
                self.tree.set(item, tags=())

        self.root.after(0, update_with_color)

    def toggle_select(self, event):
        item = self.tree.identify_row(event.y)
        column = self.tree.identify_column(event.x)
        if column == "#1" and item:
            current_value = self.tree.item(item, "values")[0]
            new_value = "☑" if current_value == "" else ""
            self.tree.item(item, values=(new_value, self.tree.item(item, "values")[1], self.tree.item(item, "values")[2]))

    def select_all_images(self):
        new_value = "☑" if self.select_all_state.get() else ""
        for item in self.tree.get_children():
            self.tree.item(item, values=(new_value, self.tree.item(item, "values")[1], self.tree.item(item, "values")[2]))
        self.select_all_state.set(not self.select_all_state.get())  # Toggle the state for next click

    def generate_key(self):
        """Generate a key for encryption based on machine-specific data"""
        machine_id = os.environ.get('COMPUTERNAME', 'default') + os.environ.get('USERNAME', 'user')
        return base64.urlsafe_b64encode(hashlib.sha256(machine_id.encode()).digest())

    def encrypt_token(self, token):
        """Encrypt the API token"""
        try:
            key = self.generate_key()
            fernet = Fernet(key)
            encrypted_token = fernet.encrypt(token.encode())
            return base64.b64encode(encrypted_token).decode()
        except Exception:
            # Fallback to simple base64 encoding if encryption fails
            return base64.b64encode(token.encode()).decode()

    def decrypt_token(self, encrypted_token):
        """Decrypt the API token"""
        try:
            key = self.generate_key()
            fernet = Fernet(key)
            decoded_token = base64.b64decode(encrypted_token.encode())
            return fernet.decrypt(decoded_token).decode()
        except Exception:
            # Fallback to simple base64 decoding if decryption fails
            try:
                return base64.b64decode(encrypted_token.encode()).decode()
            except Exception:
                return ""

    def save_token(self, token):
        """Save the API token to file"""
        try:
            encrypted_token = self.encrypt_token(token)
            config_data = {
                "api_token": encrypted_token,
                "version": "1.0"
            }
            with open(self.config_file, 'w') as f:
                json.dump(config_data, f)
            self.update_token_status("Saved")
            return True
        except Exception as e:
            messagebox.showerror("Error", f"Failed to save token: {str(e)}")
            return False

    def load_saved_token(self):
        """Load the saved API token on startup"""
        try:
            if os.path.exists(self.config_file):
                with open(self.config_file, 'r') as f:
                    config_data = json.load(f)

                encrypted_token = config_data.get("api_token", "")
                if encrypted_token:
                    decrypted_token = self.decrypt_token(encrypted_token)
                    if decrypted_token:
                        self.api_token = decrypted_token
                        self.update_token_status("Loaded")
                        return True
        except Exception:
            pass

        self.update_token_status("")
        return False

    def clear_saved_token(self):
        """Clear the saved token"""
        try:
            if os.path.exists(self.config_file):
                os.remove(self.config_file)
            self.api_token = ""  # Clear the in-memory token
            self.update_token_status("")
            messagebox.showinfo("Success", "Saved token cleared successfully.")
        except Exception as e:
            messagebox.showerror("Error", f"Failed to clear token: {str(e)}")

    def update_token_status(self, status):
        """Update the token status label"""
        if status == "Saved":
            self.token_status_label.config(text="✓ API Token Saved", foreground="green")
        elif status == "Loaded":
            self.token_status_label.config(text="✓ API Token Ready", foreground="blue")
        else:
            self.token_status_label.config(text="⚠ No API Token - Use Settings > Manage API Token", foreground="orange")

    def open_token_manager(self):
        """Open the token management dialog"""
        dialog = tk.Toplevel(self.root)
        dialog.title("API Token Manager")
        dialog.geometry("500x250")
        dialog.resizable(False, False)
        dialog.transient(self.root)
        dialog.grab_set()

        # Set dialog icon (same as main window)
        try:
            dialog.iconbitmap('app_icon.ico')
        except Exception:
            pass

        # Center the dialog
        dialog.update_idletasks()
        x = (dialog.winfo_screenwidth() // 2) - (250)  # Half of width (500/2)
        y = (dialog.winfo_screenheight() // 2) - (125)  # Half of height (250/2)
        dialog.geometry(f"500x250+{x}+{y}")

        main_frame = ttk.Frame(dialog, padding="20")
        main_frame.pack(fill=tk.BOTH, expand=True)

        # Token entry section
        entry_frame = ttk.LabelFrame(main_frame, text="API Token", padding="10")
        entry_frame.pack(fill=tk.X, pady=(0, 10))

        ttk.Label(entry_frame, text="Enter your Printify API Token:").pack(anchor=tk.W, pady=(0, 5))

        token_entry = ttk.Entry(entry_frame, width=70, show="*", font=("Consolas", 10))
        token_entry.pack(fill=tk.X, pady=(0, 5))

        # Pre-fill with current token if available
        if self.api_token:
            token_entry.insert(0, self.api_token)

        # Show/Hide token button
        show_token_var = tk.BooleanVar()
        def toggle_token_visibility():
            if show_token_var.get():
                token_entry.config(show="")
            else:
                token_entry.config(show="*")

        ttk.Checkbutton(entry_frame, text="Show token", variable=show_token_var,
                       command=toggle_token_visibility).pack(anchor=tk.W)

        # Actions section
        actions_frame = ttk.Frame(main_frame)
        actions_frame.pack(fill=tk.X, pady=(10, 0))

        def save_token():
            token = token_entry.get().strip()
            if not token:
                messagebox.showwarning("Warning", "Please enter a token first.")
                return

            # Set the token in memory and save to file
            self.api_token = token
            if self.save_token(token):
                messagebox.showinfo("Success", "Token saved successfully!")
                dialog.destroy()

        def clear_token():
            result = messagebox.askyesno("Confirm", "Are you sure you want to clear the saved token?")
            if result:
                self.clear_saved_token()
                token_entry.delete(0, tk.END)

        # Button layout
        button_frame = ttk.Frame(actions_frame)
        button_frame.pack(fill=tk.X)

        ttk.Button(button_frame, text="Save Token", command=save_token).pack(side=tk.LEFT, padx=(0, 5))
        ttk.Button(button_frame, text="Clear Token", command=clear_token).pack(side=tk.LEFT, padx=5)
        ttk.Button(button_frame, text="Close", command=dialog.destroy).pack(side=tk.RIGHT)

if __name__ == "__main__":
    root = tk.Tk()
    app = PrintifyUploader(root)
    root.mainloop()
