import os
import tkinter as tk
from tkinter import filedialog, ttk, messagebox
import base64
import requests
import threading
from tkinter import font
import json
import hashlib
from cryptography.fernet import Fernet

class PrintifyUploader:
    def __init__(self, root):
        self.root = root
        self.root.title("Printify Image Uploader")
        self.root.state('zoomed')  # Set to full screen

        self.select_all_state = tk.BooleanVar(value=True)  # Initial state for Select All (True means check all)
        self.config_file = ".printify_config.dat"
        self.api_token = ""

        self.create_menu()
        self.create_widgets()
        self.load_saved_token()

    def create_menu(self):
        menubar = tk.Menu(self.root)
        self.root.config(menu=menubar)

        # Settings menu
        settings_menu = tk.Menu(menubar, tearoff=0)
        menubar.add_cascade(label="Settings", menu=settings_menu)
        settings_menu.add_command(label="Manage API Token", command=self.open_token_manager)
        settings_menu.add_separator()
        settings_menu.add_command(label="Clear Saved Token", command=self.clear_saved_token)

    def create_widgets(self):
        # Controls frame
        controls_frame = ttk.Frame(self.root)
        controls_frame.pack(side=tk.TOP, fill=tk.X, padx=10, pady=10)

        # Token status indicator
        self.token_status_label = ttk.Label(controls_frame, text="", foreground="gray")
        self.token_status_label.pack(side=tk.LEFT, padx=5)

        self.browse_button = ttk.Button(controls_frame, text="Browse Folder", command=self.browse_folder)
        self.browse_button.pack(side=tk.LEFT, padx=5)

        self.upload_in_order_var = tk.BooleanVar(value=True)
        self.upload_in_order_checkbox = ttk.Checkbutton(controls_frame, text="Upload in Order", variable=self.upload_in_order_var)
        self.upload_in_order_checkbox.pack(side=tk.LEFT, padx=5)

        self.tree_frame = ttk.Frame(self.root)
        self.tree_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)

        # Add scrollbar to treeview
        self.tree_scrollbar = ttk.Scrollbar(self.tree_frame)
        self.tree_scrollbar.pack(side=tk.RIGHT, fill=tk.Y)

        self.tree = ttk.Treeview(self.tree_frame, columns=("Select", "Image", "Status"), show="headings", selectmode="extended", yscrollcommand=self.tree_scrollbar.set)
        self.tree.heading("Select", text="Select", command=self.select_all_images)
        self.tree.heading("Image", text="Image Files")
        self.tree.heading("Status", text="Status")
        self.tree.column("Select", width=80, anchor=tk.CENTER)
        self.tree.column("Image", width=400)
        self.tree.column("Status", width=150)
        self.tree.bind('<ButtonRelease-1>', self.toggle_select)

        self.tree.pack(fill=tk.BOTH, expand=True)
        self.tree_scrollbar.config(command=self.tree.yview)

        self.upload_button = ttk.Button(self.root, text="Upload Selected Images", command=self.upload_images)
        self.upload_button.pack(fill=tk.X, pady=10, padx=10)

        # Make the upload button larger and bold
        self.upload_button.config(style='Bold.TButton')
        style = ttk.Style()
        style.configure('Bold.TButton', font=('Helvetica', 10, 'bold'), padding=10)

    def browse_folder(self):
        folder = filedialog.askdirectory()
        if folder:
            self.load_images(folder)

    def load_images(self, folder):
        self.tree.delete(*self.tree.get_children())
        for file in os.listdir(folder):
            if file.lower().endswith(('.png', '.jpg', '.jpeg', '.svg')):
                self.tree.insert("", tk.END, values=("", os.path.join(folder, file), ""))
        self.resize_columns()

    def resize_columns(self):
        self.tree.column("Select", width=80)
        self.tree.column("Status", width=150)
        max_width = max(font.Font().measure(self.tree.item(item, "values")[1]) for item in self.tree.get_children())
        self.tree.column("Image", width=max_width + 20)

    def upload_images(self):
        if not self.api_token:
            messagebox.showwarning("Missing API Token", "Please set your API token in Settings > Manage API Token.")
            return

        if self.upload_in_order_var.get():
            threading.Thread(target=self.upload_images_in_order).start()
        else:
            for item in self.tree.get_children():
                if self.tree.item(item, "values")[0] == "☑":
                    image_path = self.tree.item(item, "values")[1]
                    self.tree.set(item, "Status", "Uploading...")
                    threading.Thread(target=self.upload_image, args=(item, image_path)).start()

    def upload_images_in_order(self):
        for item in self.tree.get_children():
            if self.tree.item(item, "values")[0] == "☑":
                image_path = self.tree.item(item, "values")[1]
                self.tree.set(item, "Status", "Uploading...")
                self.upload_image(item, image_path)

    def upload_image(self, item, image_path):
        url = "https://api.printify.com/v1/uploads/images.json"
        headers = {
            "Authorization": f"Bearer {self.api_token}",
            "Content-Type": "application/json"
        }

        with open(image_path, "rb") as image_file:
            encoded_string = base64.b64encode(image_file.read()).decode('utf-8')

        data = {
            "file_name": os.path.basename(image_path),
            "contents": encoded_string
        }

        response = requests.post(url, headers=headers, json=data)
        if response.status_code == 200:
            self.update_status(item, "Uploaded")
        else:
            self.update_status(item, f"Failed: {response.status_code}")

    def update_status(self, item, status):
        self.tree.set(item, "Status", status)

    def toggle_select(self, event):
        item = self.tree.identify_row(event.y)
        column = self.tree.identify_column(event.x)
        if column == "#1" and item:
            current_value = self.tree.item(item, "values")[0]
            new_value = "☑" if current_value == "" else ""
            self.tree.item(item, values=(new_value, self.tree.item(item, "values")[1], self.tree.item(item, "values")[2]))

    def select_all_images(self):
        new_value = "☑" if self.select_all_state.get() else ""
        for item in self.tree.get_children():
            self.tree.item(item, values=(new_value, self.tree.item(item, "values")[1], self.tree.item(item, "values")[2]))
        self.select_all_state.set(not self.select_all_state.get())  # Toggle the state for next click

    def generate_key(self):
        """Generate a key for encryption based on machine-specific data"""
        machine_id = os.environ.get('COMPUTERNAME', 'default') + os.environ.get('USERNAME', 'user')
        return base64.urlsafe_b64encode(hashlib.sha256(machine_id.encode()).digest())

    def encrypt_token(self, token):
        """Encrypt the API token"""
        try:
            key = self.generate_key()
            fernet = Fernet(key)
            encrypted_token = fernet.encrypt(token.encode())
            return base64.b64encode(encrypted_token).decode()
        except Exception:
            # Fallback to simple base64 encoding if encryption fails
            return base64.b64encode(token.encode()).decode()

    def decrypt_token(self, encrypted_token):
        """Decrypt the API token"""
        try:
            key = self.generate_key()
            fernet = Fernet(key)
            decoded_token = base64.b64decode(encrypted_token.encode())
            return fernet.decrypt(decoded_token).decode()
        except Exception:
            # Fallback to simple base64 decoding if decryption fails
            try:
                return base64.b64decode(encrypted_token.encode()).decode()
            except Exception:
                return ""

    def save_token(self, token):
        """Save the API token to file"""
        try:
            encrypted_token = self.encrypt_token(token)
            config_data = {
                "api_token": encrypted_token,
                "version": "1.0"
            }
            with open(self.config_file, 'w') as f:
                json.dump(config_data, f)
            self.update_token_status("Saved")
            return True
        except Exception as e:
            messagebox.showerror("Error", f"Failed to save token: {str(e)}")
            return False

    def load_saved_token(self):
        """Load the saved API token on startup"""
        try:
            if os.path.exists(self.config_file):
                with open(self.config_file, 'r') as f:
                    config_data = json.load(f)

                encrypted_token = config_data.get("api_token", "")
                if encrypted_token:
                    decrypted_token = self.decrypt_token(encrypted_token)
                    if decrypted_token:
                        self.api_token = decrypted_token
                        self.update_token_status("Loaded")
                        return True
        except Exception:
            pass

        self.update_token_status("")
        return False

    def clear_saved_token(self):
        """Clear the saved token"""
        try:
            if os.path.exists(self.config_file):
                os.remove(self.config_file)
            self.api_token = ""  # Clear the in-memory token
            self.update_token_status("")
            messagebox.showinfo("Success", "Saved token cleared successfully.")
        except Exception as e:
            messagebox.showerror("Error", f"Failed to clear token: {str(e)}")

    def update_token_status(self, status):
        """Update the token status label"""
        if status == "Saved":
            self.token_status_label.config(text="✓ API Token Saved", foreground="green")
        elif status == "Loaded":
            self.token_status_label.config(text="✓ API Token Ready", foreground="blue")
        elif status == "Applied":
            self.token_status_label.config(text="✓ API Token Applied (Session Only)", foreground="purple")
        else:
            self.token_status_label.config(text="⚠ No API Token - Use Settings > Manage API Token", foreground="orange")

    def open_token_manager(self):
        """Open the token management dialog"""
        dialog = tk.Toplevel(self.root)
        dialog.title("API Token Manager")
        dialog.geometry("600x450")
        dialog.resizable(True, True)
        dialog.minsize(500, 400)
        dialog.transient(self.root)
        dialog.grab_set()

        # Center the dialog
        dialog.update_idletasks()
        x = (dialog.winfo_screenwidth() // 2) - (300)  # Half of width (600/2)
        y = (dialog.winfo_screenheight() // 2) - (225)  # Half of height (450/2)
        dialog.geometry(f"600x450+{x}+{y}")

        main_frame = ttk.Frame(dialog, padding="20")
        main_frame.pack(fill=tk.BOTH, expand=True)

        # Token entry section
        entry_frame = ttk.LabelFrame(main_frame, text="API Token", padding="10")
        entry_frame.pack(fill=tk.X, pady=(0, 10))

        ttk.Label(entry_frame, text="Enter your Printify API Token:").pack(anchor=tk.W, pady=(0, 5))

        token_entry = ttk.Entry(entry_frame, width=70, show="*", font=("Consolas", 10))
        token_entry.pack(fill=tk.X, pady=(0, 5))

        # Pre-fill with current token if available
        if self.api_token:
            token_entry.insert(0, self.api_token)

        # Show/Hide token button
        show_token_var = tk.BooleanVar()
        def toggle_token_visibility():
            if show_token_var.get():
                token_entry.config(show="")
            else:
                token_entry.config(show="*")

        ttk.Checkbutton(entry_frame, text="Show token", variable=show_token_var,
                       command=toggle_token_visibility).pack(anchor=tk.W)

        # Current status section
        status_frame = ttk.LabelFrame(main_frame, text="Current Status", padding="10")
        status_frame.pack(fill=tk.X, pady=(0, 10))

        if self.api_token:
            masked_token = self.api_token[:8] + "*" * (len(self.api_token) - 12) + self.api_token[-4:] if len(self.api_token) > 12 else "*" * len(self.api_token)
            ttk.Label(status_frame, text=f"Stored Token: {masked_token}", foreground="green").pack(anchor=tk.W)
        else:
            ttk.Label(status_frame, text="No token stored", foreground="gray").pack(anchor=tk.W)

        # Actions section
        actions_frame = ttk.LabelFrame(main_frame, text="Actions", padding="10")
        actions_frame.pack(fill=tk.X, pady=(0, 10))

        def save_and_apply_token():
            token = token_entry.get().strip()
            if not token:
                messagebox.showwarning("Warning", "Please enter a token first.")
                return

            # Set the token in memory
            self.api_token = token

            # Save to file
            if self.save_token(token):
                messagebox.showinfo("Success", "Token saved and applied successfully!")
                dialog.destroy()

        def apply_token_only():
            token = token_entry.get().strip()
            if not token:
                messagebox.showwarning("Warning", "Please enter a token first.")
                return

            # Set the token in memory only (don't save to file)
            self.api_token = token
            self.update_token_status("Applied")
            messagebox.showinfo("Success", "Token applied for this session!")
            dialog.destroy()

        def load_saved_token_to_dialog():
            if self.load_saved_token():
                token_entry.delete(0, tk.END)
                token_entry.insert(0, self.api_token)
                messagebox.showinfo("Success", "Saved token loaded!")
            else:
                messagebox.showwarning("Warning", "No saved token found.")

        def clear_token():
            result = messagebox.askyesno("Confirm", "Are you sure you want to clear the saved token?")
            if result:
                self.clear_saved_token()
                token_entry.delete(0, tk.END)
                messagebox.showinfo("Success", "Token cleared!")

        ttk.Button(actions_frame, text="Save & Apply Token", command=save_and_apply_token).pack(fill=tk.X, pady=2)
        ttk.Button(actions_frame, text="Apply for This Session", command=apply_token_only).pack(fill=tk.X, pady=2)
        ttk.Button(actions_frame, text="Load Saved Token", command=load_saved_token_to_dialog).pack(fill=tk.X, pady=2)
        ttk.Button(actions_frame, text="Clear Saved Token", command=clear_token).pack(fill=tk.X, pady=2)

        # Info section
        info_frame = ttk.LabelFrame(main_frame, text="Information", padding="10")
        info_frame.pack(fill=tk.BOTH, expand=True, pady=(0, 10))

        info_text = tk.Text(info_frame, height=8, wrap=tk.WORD, state=tk.DISABLED, font=("Segoe UI", 9))
        info_scrollbar = ttk.Scrollbar(info_frame, orient="vertical", command=info_text.yview)
        info_text.configure(yscrollcommand=info_scrollbar.set)

        info_text.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        info_scrollbar.pack(side=tk.RIGHT, fill=tk.Y)

        info_content = """Token Security:
• Tokens are encrypted using machine-specific data
• Stored in a hidden configuration file (.printify_config.dat)
• Only accessible from this computer and user account
• Uses Fernet encryption with machine-specific keys

Usage:
• Save your token to avoid re-entering it each time
• The token field will be automatically filled on startup
• Use 'Clear Saved Token' if you want to remove stored credentials
• Token field shows asterisks (*) for security

Troubleshooting:
• If token doesn't load, try saving it again
• Clear and re-save if you encounter issues
• The config file is hidden in the application directory"""

        info_text.config(state=tk.NORMAL)
        info_text.insert(tk.END, info_content)
        info_text.config(state=tk.DISABLED)

        # Button frame for better layout
        button_frame = ttk.Frame(main_frame)
        button_frame.pack(fill=tk.X, pady=(10, 0))

        ttk.Button(button_frame, text="Close", command=dialog.destroy).pack(side=tk.RIGHT)

if __name__ == "__main__":
    root = tk.Tk()
    app = PrintifyUploader(root)
    root.mainloop()
