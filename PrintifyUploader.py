import os
import tkinter as tk
from tkinter import filedialog, ttk, messagebox
import base64
import requests
import threading
from tkinter import font

class PrintifyUploader:
    def __init__(self, root):
        self.root = root
        self.root.title("Printify Image Uploader")
        self.root.state('zoomed')  # Set to full screen
        
        self.select_all_state = tk.BooleanVar(value=True)  # Initial state for Select All (True means check all)
        
        self.create_widgets()

    def create_widgets(self):
        top_frame = ttk.Frame(self.root)
        top_frame.pack(side=tk.TOP, fill=tk.X, padx=10, pady=10)
        
        self.api_token_label = ttk.Label(top_frame, text="API Token:")
        self.api_token_label.pack(side=tk.LEFT, padx=5)
        
        self.api_token_entry = ttk.Entry(top_frame, width=50)
        self.api_token_entry.pack(side=tk.LEFT, padx=5)
        
        self.browse_button = ttk.Button(top_frame, text="Browse Folder", command=self.browse_folder)
        self.browse_button.pack(side=tk.LEFT, padx=5)
        
        self.upload_in_order_var = tk.BooleanVar(value=True)
        self.upload_in_order_checkbox = ttk.Checkbutton(top_frame, text="Upload in Order", variable=self.upload_in_order_var)
        self.upload_in_order_checkbox.pack(side=tk.LEFT, padx=5)
        
        self.tree_frame = ttk.Frame(self.root)
        self.tree_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)

        # Add scrollbar to treeview
        self.tree_scrollbar = ttk.Scrollbar(self.tree_frame)
        self.tree_scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
        
        self.tree = ttk.Treeview(self.tree_frame, columns=("Select", "Image", "Status"), show="headings", selectmode="extended", yscrollcommand=self.tree_scrollbar.set)
        self.tree.heading("Select", text="Select", command=self.select_all_images)
        self.tree.heading("Image", text="Image Files")
        self.tree.heading("Status", text="Status")
        self.tree.column("Select", width=80, anchor=tk.CENTER)
        self.tree.column("Image", width=400)
        self.tree.column("Status", width=150)
        self.tree.bind('<ButtonRelease-1>', self.toggle_select)
        
        self.tree.pack(fill=tk.BOTH, expand=True)
        self.tree_scrollbar.config(command=self.tree.yview)
        
        self.upload_button = ttk.Button(self.root, text="Upload Selected Images", command=self.upload_images)
        self.upload_button.pack(fill=tk.X, pady=10, padx=10)
        
        # Make the upload button larger and bold
        self.upload_button.config(style='Bold.TButton')
        style = ttk.Style()
        style.configure('Bold.TButton', font=('Helvetica', 10, 'bold'), padding=10)

    def browse_folder(self):
        folder = filedialog.askdirectory()
        if folder:
            self.load_images(folder)
    
    def load_images(self, folder):
        self.tree.delete(*self.tree.get_children())
        for file in os.listdir(folder):
            if file.lower().endswith(('.png', '.jpg', '.jpeg', '.svg')):
                self.tree.insert("", tk.END, values=("", os.path.join(folder, file), ""))
        self.resize_columns()

    def resize_columns(self):
        self.tree.column("Select", width=80)
        self.tree.column("Status", width=150)
        max_width = max(font.Font().measure(self.tree.item(item, "values")[1]) for item in self.tree.get_children())
        self.tree.column("Image", width=max_width + 20)
    
    def upload_images(self):
        self.api_token = self.api_token_entry.get()
        if not self.api_token:
            messagebox.showwarning("Missing API Token", "Please enter your API token.")
            return
        
        if self.upload_in_order_var.get():
            threading.Thread(target=self.upload_images_in_order).start()
        else:
            for item in self.tree.get_children():
                if self.tree.item(item, "values")[0] == "☑":
                    image_path = self.tree.item(item, "values")[1]
                    self.tree.set(item, "Status", "Uploading...")
                    threading.Thread(target=self.upload_image, args=(item, image_path)).start()
    
    def upload_images_in_order(self):
        for item in self.tree.get_children():
            if self.tree.item(item, "values")[0] == "☑":
                image_path = self.tree.item(item, "values")[1]
                self.tree.set(item, "Status", "Uploading...")
                self.upload_image(item, image_path)
    
    def upload_image(self, item, image_path):
        url = "https://api.printify.com/v1/uploads/images.json"
        headers = {
            "Authorization": f"Bearer {self.api_token}",
            "Content-Type": "application/json"
        }
        
        with open(image_path, "rb") as image_file:
            encoded_string = base64.b64encode(image_file.read()).decode('utf-8')
        
        data = {
            "file_name": os.path.basename(image_path),
            "contents": encoded_string
        }
        
        response = requests.post(url, headers=headers, json=data)
        if response.status_code == 200:
            self.update_status(item, "Uploaded")
        else:
            self.update_status(item, f"Failed: {response.status_code}")

    def update_status(self, item, status):
        self.tree.set(item, "Status", status)

    def toggle_select(self, event):
        item = self.tree.identify_row(event.y)
        column = self.tree.identify_column(event.x)
        if column == "#1" and item:
            current_value = self.tree.item(item, "values")[0]
            new_value = "☑" if current_value == "" else ""
            self.tree.item(item, values=(new_value, self.tree.item(item, "values")[1], self.tree.item(item, "values")[2]))

    def select_all_images(self):
        new_value = "☑" if self.select_all_state.get() else ""
        for item in self.tree.get_children():
            self.tree.item(item, values=(new_value, self.tree.item(item, "values")[1], self.tree.item(item, "values")[2]))
        self.select_all_state.set(not self.select_all_state.get())  # Toggle the state for next click

if __name__ == "__main__":
    root = tk.Tk()
    app = PrintifyUploader(root)
    root.mainloop()
